"""
雷电模拟器控制器
提供雷电模拟器实例的控制功能
"""

import os
import asyncio
import logging
from typing import Dict, List, Any, Optional

from src.devices.base import DeviceInterface, DeviceStatus, DeviceInfo

logger = logging.getLogger(__name__)

class LDPlayerController(DeviceInterface):
    """雷电模拟器控制器"""

    def __init__(self, config: Dict[str, Any]):
        """初始化雷电模拟器控制器

        Args:
            config: 配置信息
        """
        self.device_id = config.get('device_id')
        if not self.device_id:
            raise ValueError("设备配置缺少device_id字段")

        self.name = config.get('name', f'模拟器-{self.device_id}')
        self.ldconsole_path = config.get('ldconsole_path')
        if not self.ldconsole_path:
            raise ValueError("设备配置缺少ldconsole_path字段")

        if not os.path.exists(self.ldconsole_path):
            raise FileNotFoundError(f"ldconsole.exe不存在: {self.ldconsole_path}")

        self.status = DeviceStatus.UNKNOWN
        self.window_info = {}
        self.process_info = {}
        self.display_info = {}
        self.pid = 0
        self.android_ready = False  # 添加Android就绪状态标志

        logger.info(f"雷电模拟器控制器初始化，ID: {self.device_id}, 名称: {self.name}")

    async def start(self) -> bool:
        """启动设备"""
        # 先检查设备当前状态
        current_status = await self.get_status()
        if current_status == DeviceStatus.RUNNING:
            logger.info(f"设备{self.device_id}已经在运行")
            return True

        # 启动模拟器
        cmd = f'"{self.ldconsole_path}" launch --index {self.device_id}'
        logger.info(f"执行命令: {cmd}")

        try:
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                logger.error(f"启动设备{self.device_id}失败: {stderr.decode('gbk', errors='ignore')}")
                return False

            # 更新设备状态
            self.status = DeviceStatus.STARTING
            logger.info(f"设备{self.device_id}开始启动，等待Android系统就绪")

            # 等待设备启动完成
            for i in range(180):  # 增加等待时间到180秒（3分钟）
                await asyncio.sleep(1)

                # 每10秒输出一次详细日志
                if i % 10 == 0 or i < 5:
                    logger.info(f"设备{self.device_id}启动中，已等待{i+1}秒...")

                # 检查设备状态
                status = await self.get_status()
                if status == DeviceStatus.RUNNING:
                    # 检查Android系统是否已就绪
                    if self.android_ready:
                        logger.info(f"设备{self.device_id}启动成功，Android系统已就绪，总耗时{i+1}秒")
                        return True
                    else:
                        # 每10秒输出一次详细日志
                        if i % 10 == 0:
                            logger.info(f"设备{self.device_id}进程已运行，但Android系统尚未就绪，已等待{i+1}秒，继续等待...")
                        # 继续等待

            # 启动超时，检查设备是否存在进程
            cmd_list2 = f'"{self.ldconsole_path}" list2'
            process_list2 = await asyncio.create_subprocess_shell(
                cmd_list2,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout_list2, _ = await process_list2.communicate()

            if process_list2.returncode == 0:
                output_list2 = stdout_list2.decode('gbk', errors='ignore').strip()
                lines = output_list2.split('\n')
                for line in lines:
                    parts = line.split(',')
                    if len(parts) >= 7 and parts[0].strip() == self.device_id:
                        pid = parts[5].strip()
                        if pid and pid != '0' and pid != '-1' and int(pid) > 0:
                            logger.warning(f"设备{self.device_id}启动超时，但进程仍在运行(PID={pid})，可能需要手动检查")
                            # 尽管超时，但进程存在，可能是Android启动慢
                            self.status = DeviceStatus.RUNNING
                            return True

            logger.warning(f"设备{self.device_id}启动超时，Android系统未就绪")
            # 尝试强制停止设备
            await self.stop()
            return False

        except Exception as e:
            logger.error(f"启动设备{self.device_id}异常: {str(e)}", exc_info=True)
            return False

    async def stop(self) -> bool:
        """停止设备"""
        if self.status != DeviceStatus.RUNNING:
            logger.info(f"设备{self.device_id}未在运行")
            return True

        # 停止模拟器
        cmd = f'"{self.ldconsole_path}" quit --index {self.device_id}'
        logger.info(f"执行命令: {cmd}")

        try:
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                logger.error(f"停止设备{self.device_id}失败: {stderr.decode()}")
                return False

            # 更新设备状态
            self.status = DeviceStatus.STOPPING

            # 等待设备停止完成
            for _ in range(30):  # 最多等待30秒
                await asyncio.sleep(1)
                status = await self.get_status()
                if status == DeviceStatus.STOPPED:
                    logger.info(f"设备{self.device_id}停止成功")
                    return True

            logger.warning(f"设备{self.device_id}停止超时")
            return False

        except Exception as e:
            logger.error(f"停止设备{self.device_id}异常: {str(e)}", exc_info=True)
            return False

    async def restart(self) -> bool:
        """重启设备"""
        # 先停止设备
        stop_success = await self.stop()
        if not stop_success:
            logger.warning(f"停止设备{self.device_id}失败，尝试强制重启")

        # 等待一段时间
        await asyncio.sleep(2)

        # 启动设备
        start_success = await self.start()
        if not start_success:
            logger.error(f"启动设备{self.device_id}失败")
            return False

        logger.info(f"设备{self.device_id}重启成功")
        return True

    async def get_status(self) -> DeviceStatus:
        """获取设备状态"""
        # 获取设备状态
        cmd = f'"{self.ldconsole_path}" isrunning --index {self.device_id}'
        logger.debug(f"执行命令: {cmd}")

        try:
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                logger.error(f"获取设备{self.device_id}状态失败: {stderr.decode()}")
                return DeviceStatus.UNKNOWN

            # 解析输出
            output = stdout.decode().strip()

            # 更新设备状态
            if output == '1' or output.lower() == 'running':
                # 设备正在运行
                self.status = DeviceStatus.RUNNING
                logger.debug(f"设备{self.device_id}的isrunning结果为{output}，判定为RUNNING状态")

                # 额外检查设备详细信息，确保PID有效
                cmd_list2 = f'"{self.ldconsole_path}" list2'
                process_list2 = await asyncio.create_subprocess_shell(
                    cmd_list2,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout_list2, stderr_list2 = await process_list2.communicate()

                if process_list2.returncode == 0:
                    output_list2 = stdout_list2.decode('gbk', errors='ignore').strip()
                    lines = output_list2.split('\n')
                    for line in lines:
                        parts = line.split(',')
                        if len(parts) >= 7 and parts[0].strip() == self.device_id:
                            pid = parts[5].strip()
                            # 检查是否进入android（第5列）
                            android_ready = parts[4].strip() if len(parts) > 4 else '0'

                            # 只有当PID大于0时才认为设备在运行
                            if pid and pid != '0' and pid != '-1' and int(pid) > 0:
                                if android_ready == '1':
                                    # Android系统已就绪
                                    self.status = DeviceStatus.RUNNING
                                    self.pid = int(pid)
                                    self.android_ready = True
                                    logger.debug(f"设备{self.device_id}状态为RUNNING，PID={pid}，Android系统已就绪")
                                else:
                                    # 进程运行但Android系统尚未就绪
                                    self.status = DeviceStatus.STARTING
                                    self.pid = int(pid)
                                    self.android_ready = False
                                    logger.debug(f"设备{self.device_id}状态为STARTING，PID={pid}，Android系统尚未就绪")
                            else:
                                # PID无效，但isrunning返回running，可能是启动中
                                self.status = DeviceStatus.STARTING
                                self.android_ready = False
                                logger.debug(f"设备{self.device_id}的PID为{pid}，但isrunning为{output}，判定为STARTING状态")
                            break
                    else:
                        # 没有找到设备信息，但isrunning返回running，可能是启动中
                        self.status = DeviceStatus.STARTING
                        logger.debug(f"设备{self.device_id}在list2结果中未找到，但isrunning为{output}，判定为STARTING状态")
            else:
                self.status = DeviceStatus.STOPPED
                logger.debug(f"设备{self.device_id}的isrunning结果为{output}，判定为STOPPED状态")

            return self.status

        except Exception as e:
            logger.error(f"获取设备{self.device_id}状态异常: {str(e)}", exc_info=True)
            return DeviceStatus.UNKNOWN

    async def execute_command(self, command: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行设备命令"""
        if not params:
            params = {}

        # 处理特殊命令
        if command == "launch_with_network_check":
            return await self._launch_with_network_check()

        # 构建命令
        cmd_parts = [f'"{self.ldconsole_path}"', command, f'--index {self.device_id}']

        # 添加参数
        for key, value in params.items():
            cmd_parts.append(f'--{key} {value}')

        cmd = ' '.join(cmd_parts)
        logger.info(f"执行命令: {cmd}")

        try:
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                error_msg = stderr.decode('gbk', errors='ignore')
                logger.error(f"执行命令失败: {error_msg}")
                return {
                    'success': False,
                    'error': error_msg
                }

            # 解析输出 - 使用GBK编码
            output = stdout.decode('gbk', errors='ignore').strip()

            return {
                'success': True,
                'output': output
            }

        except Exception as e:
            logger.error(f"执行命令异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'error': str(e)
            }

    async def _launch_with_network_check(self) -> Dict[str, Any]:
        """启动网络检查和V2rayN连接"""
        try:
            logger.info(f"开始为设备 {self.device_id} 执行网络检查和V2rayN启动")

            # 导入必要的模块
            from ...services.common.device_manager import DeviceManager
            from ...services.common.network_manager import NetworkManager
            from ...services.common.v2ray_manager import V2rayManager

            # 创建管理器实例
            device_manager = DeviceManager(self.device_id)
            network_manager = NetworkManager(self.device_id)
            v2ray_manager = V2rayManager(self.device_id, device_manager)

            # 检查网络连接
            logger.info("检查网络连接状态...")
            network_status = await network_manager.check_network_connection()
            logger.info(f"网络状态: {network_status}")

            # 如果无法访问Google，尝试启动V2rayN
            if not network_status.get("google_accessible", False):
                if network_status.get("basic_network", False):
                    logger.info("无法访问Google但可以访问基础网络，尝试启动V2rayN...")
                else:
                    logger.info("无法访问Google和基础网络，尝试启动V2rayN...")

                # 启动V2rayN
                v2ray_success = await v2ray_manager.launch_and_connect()
                if v2ray_success:
                    logger.info("V2rayN启动成功，等待代理生效...")
                    await asyncio.sleep(5)  # 等待代理生效

                    # 重新检查Google连接
                    logger.info("重新检查Google连接...")
                    network_status = await network_manager.check_network_connection()
                    logger.info(f"启动V2rayN后的网络状态: {network_status}")

                    if network_status.get("google_accessible", False):
                        logger.info("启动V2rayN后可以访问Google，网络连接正常")
                        return {
                            'success': True,
                            'output': "V2rayN启动成功，网络连接正常"
                        }
                    else:
                        logger.warning("启动V2rayN后仍无法访问Google，但继续执行")
                        return {
                            'success': True,
                            'output': "V2rayN已启动，但网络连接仍受限"
                        }
                else:
                    logger.warning("V2rayN启动失败")
                    return {
                        'success': False,
                        'error': "V2rayN启动失败"
                    }
            else:
                logger.info("网络连接正常，可以访问Google")
                return {
                    'success': True,
                    'output': "网络连接正常，无需启动V2rayN"
                }

        except Exception as e:
            logger.error(f"网络检查和V2rayN启动异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'error': f"网络检查和V2rayN启动异常: {str(e)}"
            }

    async def get_info(self) -> DeviceInfo:
        """获取设备信息"""
        # 更新状态
        await self.get_status()

        # 如果设备在运行，获取详细信息
        if self.status == DeviceStatus.RUNNING:
            await self._update_device_details()

        return DeviceInfo(
            device_id=self.device_id,
            name=self.name,
            status=self.status,
            device_type='雷电模拟器',  # 统一设置为"雷电模拟器"
            window_info=self.window_info,
            process_info=self.process_info,
            display_info=self.display_info
        )

    async def _update_device_details(self) -> None:
        """更新设备详细信息"""
        # 获取窗口信息
        await self._get_window_info()

        # 获取进程信息
        await self._get_process_info()

        # 获取显示信息
        await self._get_display_info()

    async def _get_window_info(self) -> None:
        """获取窗口信息"""
        # 获取顶层窗口句柄
        result = await self.execute_command('getwindowhandle')

        if result.get('success'):
            self.window_info = {
                'top_window': result.get('output', '')
            }

    async def _get_process_info(self) -> None:
        """获取进程信息"""
        # 获取进程ID
        result = await self.execute_command('getpid')

        if result.get('success'):
            try:
                pid = int(result.get('output', '0'))
                self.process_info = {
                    'pid': pid
                }
            except ValueError:
                logger.warning(f"无法解析进程ID: {result.get('output')}")

    async def _get_display_info(self) -> None:
        """获取显示信息"""
        # 获取分辨率宽度
        width_result = await self.execute_command('getprop', {'key': 'resolution.width'})

        width = 0
        if width_result.get('success'):
            try:
                width = int(width_result.get('output', '0'))
            except ValueError:
                logger.warning(f"无法解析宽度: {width_result.get('output')}")

        # 获取分辨率高度
        height_result = await self.execute_command('getprop', {'key': 'resolution.height'})

        height = 0
        if height_result.get('success'):
            try:
                height = int(height_result.get('output', '0'))
            except ValueError:
                logger.warning(f"无法解析高度: {height_result.get('output')}")

        # 获取DPI
        dpi_result = await self.execute_command('getprop', {'key': 'display.dpi'})

        dpi = 0
        if dpi_result.get('success'):
            try:
                dpi = int(dpi_result.get('output', '0'))
            except ValueError:
                logger.warning(f"无法解析DPI: {dpi_result.get('output')}")

        # 更新显示信息
        self.display_info = {
            'width': width,
            'height': height,
            'dpi': dpi
        }

    async def _check_android_ready(self) -> bool:
        """检查Android系统是否已就绪

        使用多种方法检查雷电模拟器是否已完全启动，优先使用不会卡住的方法

        Returns:
            bool: Android系统是否已就绪
        """
        try:
            # 检查方法1: 检查模拟器进程是否在运行
            isrunning_result = await self.execute_command('isrunning')
            if not isrunning_result.get('success') or isrunning_result.get('output', '').strip().lower() not in ['1', 'running']:
                logger.debug(f"设备{self.device_id}进程未运行")
                return False

            # 检查方法2: 使用list2命令获取详细信息
            cmd_list2 = f'"{self.ldconsole_path}" list2'
            process_list2 = await asyncio.create_subprocess_shell(
                cmd_list2,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout_list2, _ = await process_list2.communicate()

            # 如果能获取到PID，并且PID有效，直接认为设备已就绪
            if process_list2.returncode == 0:
                output_list2 = stdout_list2.decode('gbk', errors='ignore').strip()
                lines = output_list2.split('\n')
                for line in lines:
                    parts = line.split(',')
                    if len(parts) >= 7 and parts[0].strip() == self.device_id:
                        pid = parts[5].strip()
                        title = parts[1].strip()
                        # 检查是否进入android（第5列）
                        android_ready = parts[4].strip() if len(parts) > 4 else '0'

                        # 检查PID和Android就绪状态
                        if pid and pid != '0' and pid != '-1' and int(pid) > 0:
                            if android_ready == '1':
                                logger.debug(f"设备{self.device_id}进程正在运行，PID={pid}，标题={title}，Android系统已就绪")
                                return True
                            else:
                                logger.debug(f"设备{self.device_id}进程正在运行，PID={pid}，标题={title}，但Android系统尚未就绪")
                                return False
                        else:
                            logger.debug(f"设备{self.device_id}进程信息不完整，PID={pid}，标题={title}")
                            return False
                        break
                else:
                    logger.debug(f"设备{self.device_id}在list2结果中未找到")
                    return False

            # 检查方法3: 获取窗口句柄，确认窗口已创建
            hwnd_result = await self.execute_command('getwindowhandle')
            if hwnd_result.get('success') and hwnd_result.get('output', '').strip() != '0':
                logger.debug(f"设备{self.device_id}窗口已创建，句柄: {hwnd_result.get('output', '').strip()}")
                # 如果窗口已创建，认为设备已就绪
                return True
            else:
                logger.debug(f"设备{self.device_id}窗口未创建")
                return False

            # 注意：以下方法可能会卡住，只有在前面的方法都失败时才会执行
            # 实际上，前面的方法应该已经能够确定设备是否就绪，这里只是作为备用

            # 检查方法4: 检查是否有屏幕分辨率
            try:
                # 创建一个简单的异步任务，带有超时
                async def get_resolution():
                    return await self.execute_command('getprop', {'key': 'resolution.width'})

                # 设置2秒超时
                resolution_result = await asyncio.wait_for(get_resolution(), timeout=2.0)

                if resolution_result.get('success'):
                    try:
                        width = int(resolution_result.get('output', '0'))
                        if width > 0:
                            logger.debug(f"设备{self.device_id}屏幕宽度为{width}，Android系统已就绪")
                            return True
                    except ValueError:
                        pass
            except asyncio.TimeoutError:
                logger.warning(f"设备{self.device_id}获取分辨率超时")

            # 如果以上所有检查都未能确认设备就绪，则认为设备未就绪
            logger.debug(f"设备{self.device_id}的Android系统尚未就绪")
            return False

        except Exception as e:
            logger.error(f"检查设备{self.device_id}的Android就绪状态异常: {str(e)}", exc_info=True)
            return False


