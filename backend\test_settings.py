#!/usr/bin/env python3
"""
测试配置加载的脚本
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_env_file():
    """测试.env文件是否存在和可读"""
    print("=== 测试.env文件 ===")
    
    # 检查当前工作目录
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查.env文件
    env_paths = [
        ".env",
        "../.env",
        "backend/.env",
        os.path.join(os.path.dirname(__file__), ".env"),
        os.path.join(os.path.dirname(__file__), "../.env")
    ]
    
    for path in env_paths:
        abs_path = os.path.abspath(path)
        exists = os.path.exists(path)
        print(f"路径: {path}")
        print(f"绝对路径: {abs_path}")
        print(f"存在: {exists}")
        
        if exists:
            print("文件内容:")
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(content[:200] + "..." if len(content) > 200 else content)
            except Exception as e:
                print(f"读取失败: {e}")
        print("-" * 50)

def test_env_vars():
    """测试环境变量"""
    print("\n=== 测试环境变量 ===")
    
    env_vars = [
        "APP_APP_NAME",
        "APP_CORE_DEFAULT_HOST", 
        "APP_CORE_DEFAULT_PORT",
        "APP_ACCESS_TOKEN_EXPIRE_MINUTES"
    ]
    
    for var in env_vars:
        value = os.environ.get(var)
        print(f"{var}: {value}")

def test_settings():
    """测试Settings类"""
    print("\n=== 测试Settings类 ===")
    
    try:
        from app.config.settings import Settings
        
        # 尝试直接创建Settings实例
        print("尝试创建Settings实例...")
        settings = Settings()
        
        print("Settings创建成功!")
        print(f"app_name: {settings.app_name}")
        print(f"core_default_host: {getattr(settings, 'core_default_host', 'NOT_SET')}")
        print(f"core_default_port: {getattr(settings, 'core_default_port', 'NOT_SET')}")
        print(f"access_token_expire_minutes: {getattr(settings, 'access_token_expire_minutes', 'NOT_SET')}")
        
    except Exception as e:
        print(f"Settings创建失败: {e}")
        import traceback
        traceback.print_exc()

def test_manual_env_load():
    """手动加载.env文件"""
    print("\n=== 手动加载.env文件 ===")
    
    try:
        from dotenv import load_dotenv
        
        # 尝试加载.env文件
        env_file = ".env"
        if os.path.exists(env_file):
            result = load_dotenv(env_file)
            print(f"加载.env文件结果: {result}")
            
            # 再次检查环境变量
            test_env_vars()
        else:
            print(f".env文件不存在: {os.path.abspath(env_file)}")
            
    except ImportError:
        print("python-dotenv未安装")
    except Exception as e:
        print(f"手动加载失败: {e}")

if __name__ == "__main__":
    test_env_file()
    test_env_vars()
    test_manual_env_load()
    test_settings()
