#!/usr/bin/env python3
"""
测试打开设备API的脚本
"""

import asyncio
import sys
import os
import requests
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

BASE_URL = "http://192.168.123.2:8000"

def get_auth_token():
    """获取认证token"""
    try:
        # 使用测试用户登录
        login_data = {
            "username": "test",
            "password": "test123"
        }
        
        response = requests.post(
            f"{BASE_URL}/auth/token",
            data=login_data,  # 使用form data
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        print(f"登录响应状态: {response.status_code}")
        print(f"登录响应内容: {response.text}")
        
        if response.status_code == 200:
            token_data = response.json()
            return token_data.get("access_token")
        else:
            print(f"登录失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"登录异常: {str(e)}")
        return None

def test_open_device_api():
    """测试打开设备API"""
    try:
        # 1. 获取认证token
        print("=== 步骤1: 获取认证token ===")
        token = get_auth_token()
        if not token:
            print("无法获取认证token，测试终止")
            return
        
        print(f"获取到token: {token[:20]}...")
        
        # 2. 测试打开设备API
        print("\n=== 步骤2: 测试打开设备API ===")
        account_id = "youtube_dan384613@gmail.com_1747125779"
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/social/accounts/{account_id}/open-device",
            headers=headers
        )
        
        print(f"API响应状态: {response.status_code}")
        print(f"API响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"API调用成功: {result}")
        else:
            print(f"API调用失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"测试异常: {str(e)}")
        import traceback
        traceback.print_exc()

def test_account_exists():
    """测试账号是否存在"""
    try:
        # 1. 获取认证token
        print("=== 测试账号是否存在 ===")
        token = get_auth_token()
        if not token:
            print("无法获取认证token，测试终止")
            return
        
        # 2. 获取账号列表
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(
            f"{BASE_URL}/api/v1/social/accounts",
            headers=headers,
            params={"limit": 10}
        )
        
        print(f"获取账号列表响应状态: {response.status_code}")
        
        if response.status_code == 200:
            accounts = response.json()
            print(f"找到 {len(accounts)} 个账号")
            
            target_account_id = "youtube_dan384613@gmail.com_1747125779"
            target_account = None
            
            for account in accounts:
                print(f"账号: {account.get('id')} - {account.get('username')} - {account.get('display_name')}")
                if account.get('id') == target_account_id:
                    target_account = account
            
            if target_account:
                print(f"\n找到目标账号: {target_account}")
                
                # 3. 测试获取账号关联的设备
                print("\n=== 测试获取账号关联的设备 ===")
                response = requests.get(
                    f"{BASE_URL}/api/v1/social/accounts/{target_account_id}/devices",
                    headers=headers
                )
                
                print(f"获取设备关联响应状态: {response.status_code}")
                print(f"获取设备关联响应内容: {response.text}")
                
            else:
                print(f"未找到目标账号: {target_account_id}")
        else:
            print(f"获取账号列表失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"测试异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("开始测试打开设备API...")
    test_account_exists()
    print("\n" + "="*50 + "\n")
    test_open_device_api()
