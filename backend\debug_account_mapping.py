#!/usr/bin/env python3
"""
调试账号设备映射的脚本
用于查看数据库中的实际数据结构
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from motor.motor_asyncio import AsyncIOMotorClient
from app.config.settings import get_settings

async def debug_account_mapping():
    """调试账号设备映射"""
    settings = get_settings()
    
    # 连接MongoDB
    client = AsyncIOMotorClient(settings.mongodb_url)
    db = client[settings.mongodb_database]
    
    try:
        print("=== 调试账号设备映射 ===\n")
        
        # 1. 查看指定账号的信息
        account_id = "youtube_dan384613@gmail.com_1747125779"
        print(f"1. 查找账号: {account_id}")
        
        # 尝试多种方式查找账号
        account_queries = [
            {"id": account_id},
            {"username": account_id},
            {"_id": account_id}
        ]
        
        account = None
        for i, query in enumerate(account_queries):
            print(f"   尝试查询 {i+1}: {query}")
            account = await db.social_accounts.find_one(query)
            if account:
                print(f"   ✓ 找到账号: {account}")
                break
            else:
                print(f"   ✗ 未找到")
        
        if not account:
            print("   账号不存在，查看所有账号...")
            cursor = db.social_accounts.find({}).limit(5)
            accounts = await cursor.to_list(length=5)
            print(f"   数据库中的前5个账号:")
            for acc in accounts:
                print(f"     - _id: {acc.get('_id')}")
                print(f"       id: {acc.get('id')}")
                print(f"       username: {acc.get('username')}")
                print(f"       display_name: {acc.get('display_name')}")
                print()
        
        print("\n" + "="*50)
        
        # 2. 查看设备账号映射
        print("2. 查看设备账号映射")
        cursor = db.device_account_mappings.find({}).limit(10)
        mappings = await cursor.to_list(length=10)
        
        if mappings:
            print(f"   找到 {len(mappings)} 个映射:")
            for i, mapping in enumerate(mappings):
                print(f"   映射 {i+1}:")
                print(f"     - _id: {mapping.get('_id')}")
                print(f"     - device_id: {mapping.get('device_id')}")
                print(f"     - account_id: {mapping.get('account_id')}")
                print(f"     - platform_id: {mapping.get('platform_id')}")
                print(f"     - app_id: {mapping.get('app_id')}")
                print(f"     - status: {mapping.get('status')}")
                print()
        else:
            print("   未找到任何设备账号映射")
        
        print("\n" + "="*50)
        
        # 3. 查看设备信息
        print("3. 查看设备信息")
        cursor = db.devices.find({}).limit(5)
        devices = await cursor.to_list(length=5)
        
        if devices:
            print(f"   找到 {len(devices)} 个设备:")
            for i, device in enumerate(devices):
                print(f"   设备 {i+1}:")
                print(f"     - _id: {device.get('_id')}")
                print(f"     - id: {device.get('id')}")
                print(f"     - name: {device.get('name')}")
                print(f"     - status: {device.get('status')}")
                print()
        else:
            print("   未找到任何设备")
        
        print("\n" + "="*50)
        
        # 4. 如果找到了账号，查找其映射
        if account:
            print("4. 查找账号的设备映射")
            account_mongo_id = str(account['_id'])
            account_id_field = account.get('id')
            
            possible_ids = [account_id, account_mongo_id]
            if account_id_field:
                possible_ids.append(account_id_field)
            
            print(f"   尝试的ID: {possible_ids}")
            
            for aid in possible_ids:
                print(f"   查找 account_id = {aid}")
                mapping = await db.device_account_mappings.find_one({"account_id": aid})
                if mapping:
                    print(f"   ✓ 找到映射: {mapping}")
                    break
                else:
                    print(f"   ✗ 未找到")
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        client.close()

if __name__ == "__main__":
    asyncio.run(debug_account_mapping())
