/**
 * 视频时长分类工具
 */

// 视频时长分类枚举
export enum VideoDurationCategory {
  VERY_SHORT = 'very_short',    // 35秒以下
  SHORT = 'short',              // 35~60秒
  MEDIUM = 'medium',            // 60秒~3分钟
  LONG = 'long',                // 3分钟~8分钟
  VERY_LONG = 'very_long'       // 8分钟以上
}

// 视频时长分类信息
export interface VideoDurationInfo {
  category: VideoDurationCategory
  duration: number
  message: string
  color: string
  bgColor: string
  description: string
}

/**
 * 根据视频时长（秒）计算分类信息
 * @param duration 视频时长（秒）
 * @returns 视频时长分类信息
 */
export function calculateVideoDurationCategory(duration?: number): VideoDurationInfo {
  // 如果没有时长信息，返回默认状态
  if (!duration || duration <= 0) {
    return {
      category: VideoDurationCategory.VERY_SHORT,
      duration: 0,
      message: '未知时长',
      color: '#909399',
      bgColor: '#f4f4f5',
      description: '无法获取视频时长信息'
    }
  }

  // 35秒以下 - 极短视频
  if (duration < 35) {
    return {
      category: VideoDurationCategory.VERY_SHORT,
      duration,
      message: '极短视频',
      color: '#67c23a',
      bgColor: '#f6ffed',
      description: '适合快速浏览和社交分享'
    }
  }

  // 35~60秒 - 短视频
  if (duration >= 35 && duration <= 60) {
    return {
      category: VideoDurationCategory.SHORT,
      duration,
      message: '短视频',
      color: '#e6a23c',
      bgColor: '#fff7e6',
      description: '标准短视频长度，适合大多数平台'
    }
  }

  // 60秒~3分钟 - 中等视频
  if (duration > 60 && duration <= 180) {
    return {
      category: VideoDurationCategory.MEDIUM,
      duration,
      message: '中等视频',
      color: '#409eff',
      bgColor: '#e6f4ff',
      description: '中等长度，适合详细内容展示'
    }
  }
  
  // 3分钟~8分钟 - 长视频
  if (duration > 180 && duration <= 480) {
    return {
      category: VideoDurationCategory.LONG,
      duration,
      message: '长视频',
      color: '#f78989',
      bgColor: '#fef0f0',
      description: '较长内容，需要用户较多时间投入'
    }
  }
  
  // 8分钟以上 - 超长视频
  return {
    category: VideoDurationCategory.VERY_LONG,
    duration,
    message: '超长视频',
    color: '#f56c6c',
    bgColor: '#fef0f0',
    description: '超长内容，适合深度学习和教程'
  }
}

/**
 * 格式化视频时长显示
 * @param seconds 时长（秒）
 * @returns 格式化后的时长字符串
 */
export function formatVideoDuration(seconds?: number): string {
  if (!seconds || seconds <= 0) return '未知'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
}

/**
 * 获取视频时长分类的CSS类名
 * @param category 视频时长分类
 * @returns CSS类名
 */
export function getVideoDurationClass(category: VideoDurationCategory): string {
  switch (category) {
    case VideoDurationCategory.VERY_SHORT:
      return 'video-very-short'
    case VideoDurationCategory.SHORT:
      return 'video-short'
    case VideoDurationCategory.MEDIUM:
      return 'video-medium'
    case VideoDurationCategory.LONG:
      return 'video-long'
    case VideoDurationCategory.VERY_LONG:
      return 'video-very-long'
    default:
      return 'video-unknown'
  }
}

/**
 * 获取视频时长分类的文本CSS类名
 * @param category 视频时长分类
 * @returns 文本CSS类名
 */
export function getVideoDurationTextClass(category: VideoDurationCategory): string {
  switch (category) {
    case VideoDurationCategory.VERY_SHORT:
      return 'duration-text-very-short'
    case VideoDurationCategory.SHORT:
      return 'duration-text-short'
    case VideoDurationCategory.MEDIUM:
      return 'duration-text-medium'
    case VideoDurationCategory.LONG:
      return 'duration-text-long'
    case VideoDurationCategory.VERY_LONG:
      return 'duration-text-very-long'
    default:
      return 'duration-text-unknown'
  }
}

/**
 * 获取视频时长的详细描述
 * @param duration 时长（秒）
 * @returns 详细描述字符串
 */
export function getVideoDurationDescription(duration?: number): string {
  const info = calculateVideoDurationCategory(duration)
  const formattedDuration = formatVideoDuration(duration)
  
  return `${info.message} (${formattedDuration}) - ${info.description}`
}

/**
 * 判断是否为视频文件
 * @param file 文件对象
 * @returns 是否为视频文件
 */
export function isVideoFile(file: any): boolean {
  if (!file || file.is_directory) return false

  const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm', '.m4v']

  // 尝试从不同字段获取扩展名
  let extension = ''

  if (file.extension) {
    extension = file.extension.toLowerCase()
    // 如果扩展名没有点，添加点
    if (!extension.startsWith('.')) {
      extension = '.' + extension
    }
  } else if (file.name) {
    // 从文件名中提取扩展名
    const lastDotIndex = file.name.lastIndexOf('.')
    if (lastDotIndex > 0) {
      extension = file.name.substring(lastDotIndex).toLowerCase()
    }
  }

  return videoExtensions.includes(extension)
}

/**
 * 获取视频时长（从不同的数据结构中）
 * @param file 文件对象
 * @returns 视频时长（秒）
 */
export function getVideoDurationFromFile(file: any): number {
  if (!file || !isVideoFile(file)) return 0

  // 尝试从不同的字段获取时长
  let duration = 0

  // 检查各种可能的时长字段
  if (file.media_info?.duration) {
    duration = file.media_info.duration
  } else if (file.duration) {
    duration = file.duration
  } else if (file.file_info?.duration) {
    duration = file.file_info.duration
  } else if (file.metadata?.duration) {
    duration = file.metadata.duration
  } else if (file.video_info?.duration) {
    duration = file.video_info.duration
  }

  // 如果时长是字符串格式（如 "1:28"），转换为秒数
  if (typeof duration === 'string') {
    duration = parseTimeStringToSeconds(duration)
  }

  return duration || 0
}

/**
 * 将时间字符串转换为秒数
 * @param timeString 时间字符串，如 "1:28" 或 "0:01:28"
 * @returns 秒数
 */
function parseTimeStringToSeconds(timeString: string): number {
  if (!timeString || typeof timeString !== 'string') return 0

  // 移除空格并分割
  const parts = timeString.trim().split(':').map(part => parseInt(part, 10))

  if (parts.length === 2) {
    // MM:SS 格式
    const [minutes, seconds] = parts
    return (minutes || 0) * 60 + (seconds || 0)
  } else if (parts.length === 3) {
    // HH:MM:SS 格式
    const [hours, minutes, seconds] = parts
    return (hours || 0) * 3600 + (minutes || 0) * 60 + (seconds || 0)
  }

  return 0
}
